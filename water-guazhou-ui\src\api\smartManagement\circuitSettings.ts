// 智慧管理-培训配置
import request from '@/plugins/axios';

// 分页查询培训配置列表
export function getCircuitSettingsList(params: {
  page: number;
  size: number;
  name?: string;
  code?: string;
  type?: string;
  status?: string;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: '/api/sm/circuitSettings',
    method: 'get',
    params
  });
}

// 根据ID查询培训配置详情
export function getCircuitSettingsById(id: string) {
  return request({
    url: `/api/sm/circuitSettings/${id}`,
    method: 'get'
  });
}

// 新增培训配置
export function addCircuitSettings(params: {
  name: string;
  code: string;
  type: string;
  status?: string;
}) {
  return request({
    url: '/api/sm/circuitSettings',
    method: 'post',
    data: params
  });
}

// 修改培训配置
export function updateCircuitSettings(id: string, params: {
  name?: string;
  code?: string;
  type?: string;
  status?: string;
}) {
  return request({
    url: `/api/sm/circuitSettings/${id}`,
    method: 'put',
    data: params
  });
}

// 删除培训配置
export function deleteCircuitSettings(id: string) {
  return request({
    url: `/api/sm/circuitSettings/${id}`,
    method: 'delete'
  });
}
