<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.plan.CircuitSettingsMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        id,
        name,
        code,
        type,
        create_time,
        status,
        tenant_id
        <!--@sql from sm_circuit_settings -->
    </sql>
    
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitSettings">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sm_circuit_settings
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND code LIKE CONCAT('%', #{code}, '%')
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <update id="update" parameterType="org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitSettings">
        UPDATE sm_circuit_settings
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="code != null and code != ''">
                code = #{code},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sm_circuit_settings
        WHERE id = #{id}
    </select>
</mapper>
