package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitSettings;

@Service
public class CircuitSettingsServiceImpl implements CircuitSettingsService{
    @Override
    public IPage<CircuitSettings> findAllConditional(CircuitSettings circuitSettings) {
        return null;
    }

    @Override
    public CircuitSettings save(CircuitSettings entity) {
        return null;
    }

    @Override
    public boolean update(CircuitSettings entity) {
        return false;
    }

    @Override
    public boolean delete(String id) {
        return false;
    }

    @Override
    public CircuitSettings findById(String id) {
        return null;
    }
}
