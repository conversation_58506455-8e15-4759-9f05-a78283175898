package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitSettings;
import org.thingsboard.server.dao.sql.smartManagement.plan.CircuitSettingsMapper;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitSettingsPageRequest;

import java.util.Date;
import java.util.UUID;

@Service
public class CircuitSettingsServiceImpl implements CircuitSettingsService{

    @Autowired
    private CircuitSettingsMapper circuitSettingsMapper;

    @Override
    public IPage<CircuitSettings> findAllConditional(CircuitSettingsPageRequest request) {
        return circuitSettingsMapper.findByPage(request);
    }

    @Override
    public CircuitSettings save(CircuitSettings entity) {
        if (entity.getId() == null || entity.getId().isEmpty()) {
            entity.setId(UUID.randomUUID().toString());
            entity.setCreateTime(new Date());
            circuitSettingsMapper.insert(entity);
        } else {
            circuitSettingsMapper.updateById(entity);
        }
        return entity;
    }

    @Override
    public boolean update(CircuitSettings entity) {
        return circuitSettingsMapper.updateById(entity) > 0;
    }

    @Override
    public boolean delete(String id) {
        return circuitSettingsMapper.deleteById(id) > 0;
    }

    @Override
    public CircuitSettings findById(String id) {
        return circuitSettingsMapper.selectById(id);
    }
}
