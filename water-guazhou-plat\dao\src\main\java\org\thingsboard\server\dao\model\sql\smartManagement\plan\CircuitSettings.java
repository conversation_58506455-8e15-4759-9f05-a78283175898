package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sm_circuit_settings")
public class CircuitSettings {

    private String id;

    //  巡检配置名称
    private String name;

    //  巡检配置编码
    private String code;

    //  巡检配置类型(0管网，1泵站，2其他)
    private String type;

    //  创建时间
    private Date createTime;

    //  状态(0启用，1停用)
    private String status;

    private String tenantId;
}
