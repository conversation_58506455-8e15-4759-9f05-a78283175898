package org.thingsboard.server.controller.smartManagement.plan;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitSettings;
import org.thingsboard.server.dao.plan.CircuitSettingsService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.controller.base.BaseController;

@RestController
@RequestMapping("/api/sm/circuitSettings")
public class CircuitSettingsController extends BaseController {
    @Autowired
    private CircuitSettingsService circuitSettingsService;

    @GetMapping
    public IPage<CircuitSettings> findAllConditional(CircuitSettings circuitSettings) {
        return circuitSettingsService.findAllConditional(circuitSettings);
    }

    @GetMapping("/{id}")
    public CircuitSettings findById(@PathVariable String id) {
        return circuitSettingsService.findById(id);
    }

    @PostMapping
    public CircuitSettings save(@RequestBody CircuitSettings entity) {
        return circuitSettingsService.save(entity);
    }

    @PutMapping("/{id}")
    public boolean update(@RequestBody CircuitSettings entity, @PathVariable String id) {
        return circuitSettingsService.update(entity);
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return circuitSettingsService.delete(id);
    }
}