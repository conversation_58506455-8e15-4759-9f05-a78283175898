package org.thingsboard.server.controller.smartManagement.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitSettings;
import org.thingsboard.server.dao.plan.CircuitSettingsService;
import org.thingsboard.server.dao.plan.CircuitSettingsServiceImpl;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitSettingsPageRequest;

@RestController
@RequestMapping("/api/sm/circuitSettings")
public class CircuitSettingsController extends BaseController {
    @Autowired
    private CircuitSettingsService circuitSettingsService;

    @GetMapping
    public IPage<CircuitSettings> findAllConditional(CircuitSettingsPageRequest request) throws ThingsboardException {
        // 设置租户ID
        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));

        // 创建查询条件对象
        CircuitSettings queryEntity = new CircuitSettings();
        queryEntity.setName(request.getName());
        queryEntity.setCode(request.getCode());
        queryEntity.setType(request.getType());
        queryEntity.setStatus(request.getStatus());
        queryEntity.setTenantId(request.getTenantId());

        // 使用带分页参数的方法
        return ((CircuitSettingsServiceImpl) circuitSettingsService)
                .findAllConditional(queryEntity, request.getPage(), request.getSize());
    }

    @GetMapping("/{id}")
    public CircuitSettings findById(@PathVariable String id) {
        return circuitSettingsService.findById(id);
    }

    @PostMapping
    public CircuitSettings save(@RequestBody CircuitSettings entity) throws ThingsboardException {
        // 设置租户ID
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return circuitSettingsService.save(entity);
    }

    @PutMapping("/{id}")
    public boolean update(@RequestBody CircuitSettings entity, @PathVariable String id) {
        entity.setId(id);
        return circuitSettingsService.update(entity);
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return circuitSettingsService.delete(id);
    }
}