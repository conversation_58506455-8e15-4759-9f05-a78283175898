package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitSettings;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class CircuitSettingsPageRequest extends AdvancedPageableQueryEntity<CircuitSettings, CircuitSettingsPageRequest> {

    // 巡检配置名称
    private String name;

    // 巡检配置编码
    private String code;

    // 巡检配置类型(0管网，1泵站，2其他)
    private String type;

    // 状态(0启用，1停用)
    private String status;

    // 创建时间范围查询
    private String fromTime;
    private String toTime;

    // 租户ID
    private String tenantId;
}
