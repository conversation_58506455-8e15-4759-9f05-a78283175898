/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.EntityType;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.audit.ActionType;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.RoleId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.common.data.security.UserCredentials;
import org.thingsboard.server.dao.app.AppMsgPushService;
import org.thingsboard.server.dao.optionLog.OptionLogService;
import org.thingsboard.server.dao.role.RoleService;
import org.thingsboard.server.dao.util.imodel.Environment;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.security.auth.jwt.RefreshTokenRepository;
import org.thingsboard.server.service.security.model.SecurityUser;
import org.thingsboard.server.service.security.model.UserPrincipal;
import org.thingsboard.server.service.security.model.token.JwtToken;
import org.thingsboard.server.service.security.model.token.JwtTokenFactory;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/api")
public class UserController extends BaseController {

    public static final String USER_ID = "userId";
    public static final String YOU_DON_T_HAVE_PERMISSION_TO_PERFORM_THIS_OPERATION = "You don't have permission to perform this operation!";
    public static final String ACTIVATE_URL_PATTERN = "%s/api/noauth/activate?activateToken=%s";

    @Autowired
    private OptionLogService optionLogService;

    @Autowired
    private MailService mailService;

    @Value("${security.user_token_access_enabled}")
    @Getter
    private boolean userTokenAccessEnabled;

    @Autowired
    private JwtTokenFactory tokenFactory;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Autowired
    private RoleService roleService;

    @Autowired
    private AppMsgPushService appMsgPushService;


    @GetMapping("user/getAll")
    public List<User> getAllUser() {
        return userService.findAll();
    }

    @GetMapping("user/getAllByPid")
    public IstarResponse getAllUser(@RequestParam(required = false, defaultValue = "") String pid,
                                    @RequestParam(required = false, defaultValue = "") String name,
                                    @RequestParam(required = false, defaultValue = "") String roleId,
                                    Boolean status,
                                    @RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(userService.findAllByPid(pid, name, roleId,tenantId, status, page, size));
    }

    @GetMapping("user/getAllByName")
    public IstarResponse getAllUser(@RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(userService.getAllUser(name, tenantId));
    }

    @GetMapping("user/getAllByPidStr")
    public IstarResponse getAllByPidStr(@RequestParam(required = false, defaultValue = "") String pid) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(userService.getAllByPidStr(pid, tenantId));
    }

    @GetMapping("user/current")
    public User getCurrentUserInfo() throws ThingsboardException {
        return userService.findUserById(getCurrentUser().getId());
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/user/{userId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_USER_INFO)
    @ResponseBody
    public User getUserById(@PathVariable(USER_ID) String strUserId) throws ThingsboardException {
        checkParameter(USER_ID, strUserId);
        try {
            if ("tokenAccessEnabled".equals(strUserId)) {
                return getCurrentUser();
            }
            UserId userId = new UserId(toUUID(strUserId));
            SecurityUser authUser = getCurrentUser();
            if (authUser.getAuthority() == Authority.CUSTOMER_USER && !authUser.getId().equals(userId)) {
                throw new ThingsboardException(YOU_DON_T_HAVE_PERMISSION_TO_PERFORM_THIS_OPERATION,
                        ThingsboardErrorCode.PERMISSION_DENIED);
            }
            return checkUserId(userId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/user/{userId}/token", method = RequestMethod.GET)
    @ResponseBody
    public JsonNode getUserToken(@PathVariable(USER_ID) String strUserId) throws ThingsboardException {
        checkParameter(USER_ID, strUserId);
        try {
            if (!userTokenAccessEnabled) {
                throw new ThingsboardException(YOU_DON_T_HAVE_PERMISSION_TO_PERFORM_THIS_OPERATION,
                        ThingsboardErrorCode.PERMISSION_DENIED);
            }
            UserId userId = new UserId(toUUID(strUserId));
            SecurityUser authUser = getCurrentUser();
            User user = checkUserId(userId);
            UserPrincipal principal = new UserPrincipal(UserPrincipal.Type.USER_NAME, user.getEmail());
            UserCredentials credentials = userService.findUserCredentialsByUserId(authUser.getTenantId(), userId);
            SecurityUser securityUser = new SecurityUser(user, credentials.isEnabled(), principal);
            JwtToken accessToken = tokenFactory.createAccessJwtToken(securityUser);
            JwtToken refreshToken = refreshTokenRepository.requestRefreshToken(securityUser);
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode tokenObject = objectMapper.createObjectNode();
            tokenObject.put("token", accessToken.getToken());
            tokenObject.put("refreshToken", refreshToken.getToken());
            return tokenObject;
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 创建用户（租户管理员）
     *
     * @param user
     * @param sendActivationMail
     * @param request
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/user", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_ADD)
    @ResponseBody
    public User saveUser(@RequestBody User user,
                         @RequestParam(required = false, defaultValue = "true") boolean sendActivationMail,
                         HttpServletRequest request, @RequestParam(value = "istarCreatePasswordUrl", required = false) String istarCreatePasswordUrl) throws ThingsboardException {
        try {
            SecurityUser authUser = getCurrentUser();
            if (authUser.getAuthority() == Authority.CUSTOMER_USER && !authUser.getId().equals(user.getId())) {
                throw new ThingsboardException(YOU_DON_T_HAVE_PERMISSION_TO_PERFORM_THIS_OPERATION,
                        ThingsboardErrorCode.PERMISSION_DENIED);
            }
            boolean sendEmail = user.getId() == null && sendActivationMail;
            if (getCurrentUser().getAuthority() == Authority.TENANT_ADMIN) {
                user.setTenantId(getTenantId());
            }
            User savedUser = checkNotNull(userService.saveUser(user));
            if (sendEmail) {
                UserCredentials userCredentials = userService.findUserCredentialsByUserId(getTenantId(), savedUser.getId());
                String baseUrl = constructBaseUrl(request);
                String activateUrl = String.format(ACTIVATE_URL_PATTERN, baseUrl,
                        userCredentials.getActivateToken());
                String email = savedUser.getEmail();
                try {
                    // qing添加,如果传上来的url,就按传上来的url执行,如果没有按照thingsboard执行
                    if (!StringUtils.isEmpty(istarCreatePasswordUrl) && istarCreatePasswordUrl.contains("http"))
                        mailService.sendActivationEmail(URLDecoder.decode(istarCreatePasswordUrl) + "?activateToken=" + userCredentials.getActivateToken(), email);
                    else
                        mailService.sendActivationEmail(activateUrl, email);
                } catch (ThingsboardException e) {
                    userService.deleteUser(getTenantId(), savedUser.getId());
                    throw e;
                }
            }

            logEntityAction(savedUser.getId(), savedUser,
                    savedUser.getCustomerId(),
                    user.getId() == null ? ActionType.ADDED : ActionType.UPDATED, null);

            return savedUser;
        } catch (Exception e) {

            logEntityAction(emptyId(EntityType.USER), user,
                    null, user.getId() == null ? ActionType.ADDED : ActionType.UPDATED, e);

            throw handleException(e);
        }
    }

    /**
     * 创建用户
     *
     * @param user
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/newUser", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_ADD)
    @ResponseBody
    public User saveUser(@RequestBody User user) throws ThingsboardException {
        try {
            SecurityUser authUser = getCurrentUser();
            if (authUser.getAuthority() == Authority.CUSTOMER_USER && !authUser.getId().equals(user.getId())) {
                throw new ThingsboardException(YOU_DON_T_HAVE_PERMISSION_TO_PERFORM_THIS_OPERATION,
                        ThingsboardErrorCode.PERMISSION_DENIED);
            }
            if (user.getAuthority() == Authority.CUSTOMER_USER) {
                TenantId tenantId = getTenantId();
                user.setTenantId(tenantId);
            }
            /*if (!StringUtils.isEmpty(user.getPassword())) {
                boolean b = checkPassword(user.getPassword());
                if (b) {
                    user.setPassword(passwordEncoder.encode(user.getPassword()));
                } else {
                    throw new ThingsboardException("密码强度过低, 请输入8到16位大小写字母加特殊字符的组合!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
                }
            }*/
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            User savedUser = checkNotNull(userService.saveUser(user));

            logEntityAction(savedUser.getId(), savedUser,
                    savedUser.getCustomerId(),
                    user.getId() == null ? ActionType.ADDED : ActionType.UPDATED, null);

            return savedUser;
        } catch (Exception e) {

            logEntityAction(emptyId(EntityType.USER), user,
                    null, user.getId() == null ? ActionType.ADDED : ActionType.UPDATED, e);

            throw handleException(e);
        }
    }

    /**
     * 更新
     *
     * @param user
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/updateUser", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_ADD)
    @ResponseBody
    public User updateUser(@RequestBody User user) throws ThingsboardException {
        // 修改密码
        if (org.apache.commons.lang3.StringUtils.isNotBlank(user.getPassword())) {
            UserCredentials userCredentials = userService.findUserCredentialsByUserId(getTenantId(), user.getId());
            userCredentials.setPassword(passwordEncoder.encode(user.getPassword()));
            userService.saveUserCredentials(getTenantId(), userCredentials);
        }
        //个人中心修改基础信息
        userService.updateUser(user);
        invalidateUserCache(user);
        return user;
    }

    /*  一、假定密码字符数范围6-16，除英文数字和字母外的字符都视为特殊字符：
    弱：^[0-9A-Za-z]{6,16}$
    中：^(?=.{6,16})[0-9A-Za-z]*[^0-9A-Za-z][0-9A-Za-z]*$
    强：^(?=.{6,16})([0-9A-Za-z]*[^0-9A-Za-z][0-9A-Za-z]*){2,}$
    二、假定密码字符数范围6-16，密码字符允许范围为ASCII码表字符：
    弱：^[0-9A-Za-z]{6,16}$
    中：^(?=.{6,16})[0-9A-Za-z]*[\x00-\x2f\x3A-\x40\x5B-\xFF][0-9A-Za-z]*$
    强：^(?=.{6,16})([0-9A-Za-z]*[\x00-\x2F\x3A-\x40\x5B-\xFF][0-9A-Za-z]*){2,}$*/
    public static boolean checkPassword(String passwordStr) {
        // 8-16位，大小写字母数字和特殊字符的组合
        String regexZST = "^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,16}$";

        if (passwordStr.matches(regexZST)) {
            return true;
        }
        return false;

    }


    /**
     * 分配项目给用户
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/assign/user/{userId}", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_ADD)
    @ResponseBody
    public void assignUser(@RequestBody List<String> projectIds, @PathVariable String userId) throws ThingsboardException {
        try {
            userService.assignUserToProjects(projectIds, userId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 分配项目给用户
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/assign/users", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_ADD)
    @ResponseBody
    public void assignUsers(@RequestBody JSONObject params) throws ThingsboardException {
        List<String> projectIds = params.getJSONArray("projectIds").toJavaList(String.class);
        List<String> userIds = params.getJSONArray("userIds").toJavaList(String.class);
        try {
            for (String userId : userIds) {
                userService.assignUserToProjects(projectIds, userId);
            }
        } catch (Exception e) {
            throw handleException(e);
        }
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/user/sendActivationMail", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_USER_SEND_EMAIL)
    @ResponseStatus(value = HttpStatus.OK)
    public void sendActivationEmail(
            @RequestParam(value = "email") String email,
            HttpServletRequest request) throws ThingsboardException {
        try {
            User user = checkNotNull(userService.findUserByEmail(getTenantId(), email));
            UserCredentials userCredentials = userService.findUserCredentialsByUserId(getTenantId(), user.getId());
            if (!userCredentials.isEnabled()) {
                String baseUrl = constructBaseUrl(request);
                String activateUrl = String.format(ACTIVATE_URL_PATTERN, baseUrl,
                        userCredentials.getActivateToken());
                mailService.sendActivationEmail(activateUrl, email);
            } else {
                throw new ThingsboardException("User is already active!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/user/{userId}/activationLink", method = RequestMethod.GET, produces = "text/plain")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_ACTIVE)
    @ResponseBody
    public String getActivationLink(
            @PathVariable(USER_ID) String strUserId,
            HttpServletRequest request) throws ThingsboardException {
        checkParameter(USER_ID, strUserId);
        try {
            UserId userId = new UserId(toUUID(strUserId));
            SecurityUser authUser = getCurrentUser();
            if (authUser.getAuthority() == Authority.CUSTOMER_USER && !authUser.getId().equals(userId)) {
                throw new ThingsboardException(YOU_DON_T_HAVE_PERMISSION_TO_PERFORM_THIS_OPERATION,
                        ThingsboardErrorCode.PERMISSION_DENIED);
            }
            User user = checkUserId(userId);
            UserCredentials userCredentials = userService.findUserCredentialsByUserId(getTenantId(), user.getId());
            if (!userCredentials.isEnabled()) {
                String baseUrl = constructBaseUrl(request);
                String activateUrl = String.format(ACTIVATE_URL_PATTERN, baseUrl,
                        userCredentials.getActivateToken());
                return activateUrl;
            } else {
                throw new ThingsboardException("User is already active!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/user/{userId}", method = RequestMethod.DELETE)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_DELETE)
    @ResponseStatus(value = HttpStatus.OK)
    public void deleteUser(@PathVariable(USER_ID) String strUserId) throws ThingsboardException {
        checkParameter(USER_ID, strUserId);
        try {
            UserId userId = new UserId(toUUID(strUserId));
            User user = checkUserId(userId);
            userService.deleteUser(getTenantId(), userId);
            invalidateUserCache(user);

            logEntityAction(userId, user,
                    user.getCustomerId(),
                    ActionType.DELETED, null, strUserId);

        } catch (Exception e) {
            logEntityAction(emptyId(EntityType.USER),
                    null,
                    null,
                    ActionType.DELETED, e, strUserId);
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/users", method = RequestMethod.DELETE)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_USER_DELETE)
    @ResponseStatus(value = HttpStatus.OK)
    public void deleteUsers(@RequestBody List<String> userIdList) throws ThingsboardException {
        for (String userId : userIdList) {
            this.deleteUser(userId);
            invalidateUserCache(UUIDConverter.fromTimeUUID(UUID.fromString(userId)));
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenan/{tenantId}/users", params = {"limit"}, method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_USER_LIST)
    @ResponseBody
    public TextPageData<User> getTenantAdmins(
            @PathVariable("tenantId") String strTenantId,
            @RequestParam int limit,
            @RequestParam(required = false) String textSearch,
            @RequestParam(required = false) String idOffset,
            @RequestParam(required = false) String textOffset) throws ThingsboardException {
        checkParameter("tenantId", strTenantId);
        try {
            TenantId tenantId = new TenantId(toUUID(strTenantId));
            TextPageLink pageLink = createPageLink(limit, textSearch, idOffset, textOffset);
            return checkNotNull(userService.findTenantAdmins(tenantId, pageLink));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/customer/{customerId}/users", params = {"limit"}, method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_USER_LIST)
    @ResponseBody
    public TextPageData<User> getCustomerUsers(
            @PathVariable("customerId") String strCustomerId,
            @RequestParam int limit,
            @RequestParam(required = false) String textSearch,
            @RequestParam(required = false) String idOffset,
            @RequestParam(required = false) String textOffset) throws ThingsboardException {
        checkParameter("customerId", strCustomerId);
        try {
            CustomerId customerId = new CustomerId(toUUID(strCustomerId));
            checkCustomerId(customerId);
            TextPageLink pageLink = createPageLink(limit, textSearch, idOffset, textOffset);
            TenantId tenantId = getTenantId();
            return checkNotNull(userService.findCustomerUsers(tenantId, customerId, pageLink));
        } catch (Exception e) {
            throw handleException(e);
        }
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/users/{tenantId}", params = {"limit"}, method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_USER_LIST)
    @ResponseBody
    public TextPageData<User> getTenantUsers(
            @PathVariable("tenantId") String strTenantId,
            @RequestParam int limit,
            @RequestParam(required = false) String textSearch,
            @RequestParam(required = false) String idOffset,
            @RequestParam(required = false) String textOffset) throws ThingsboardException {
        checkParameter("tenantId", strTenantId);
        try {
            TenantId tenantId = new TenantId(toUUID(strTenantId));
            TextPageLink pageLink = createPageLink(limit, textSearch, idOffset, textOffset);
            return checkNotNull(userService.findTenantUsers(tenantId, pageLink));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/users/list", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_USER_LIST)
    @ResponseBody
    public PageData<User> getTenantUsers(@RequestParam Integer page,
                                         @RequestParam Integer size,
                                         @RequestParam(required = false, defaultValue = "") String authority,
                                         @RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        try {

            return userService.findList(page, size, name, authority, UUIDConverter.fromTimeUUID(getTenantId().getId()));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/users/listByAuth", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_USER_LIST)
    @ResponseBody
    public List<User> getTenantUsers(@RequestParam(required = false, defaultValue = "") String authType) throws ThingsboardException {
        try {
            return userService.findListByAuth(authType, getCurrentUser());
        } catch (Exception e) {
            throw handleException(e);
        }
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/adminAndsys/{tenantId}", params = {"limit"}, method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_USER_LIST)
    @ResponseBody
    public TextPageData<User> getTenantSysAndAdmin(
            @PathVariable("tenantId") String strTenantId,
            @RequestParam int limit,
            @RequestParam(required = false) String textSearch,
            @RequestParam(required = false) String idOffset,
            @RequestParam(required = false) String textOffset) throws ThingsboardException {
        checkParameter("tenantId", strTenantId);
        try {
            TenantId tenantId = new TenantId(toUUID(strTenantId));
            checkTenantId(tenantId);
            TextPageLink pageLink = createPageLink(limit, textSearch, idOffset, textOffset);
            return checkNotNull(userService.findTenantSysAndAdmin(tenantId, pageLink));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    /**
     * 获取当前用户最后一次登录时间和IP
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @GetMapping("/user/login/info")
    public Object getUserLoginInfo() throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        if (user == null) {
            return null;
        }
        return optionLogService.getUserLoginInfo(user.getId());
    }

    /**
     * 冻结用户
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/user/disable/{userId}")
    public void disableUser(@PathVariable String userId) {
        userService.changeStatus(userId, "f");
    }

    /**
     * 解冻用户
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/user/enable/{userId}")
    public void enableUser(@PathVariable String userId) {
        userService.changeStatus(userId, "t");
    }

    @GetMapping("/user/export")
    public void exportUser(HttpServletResponse response) throws ThingsboardException {
        TenantId tenantId = getTenantId();
        TextPageLink pageLink = createPageLink(99999, null, null, null);
        TextPageData<User> tenantUsers = userService.findTenantUsers(tenantId, pageLink);
        List<User> data = tenantUsers.getData();
        if (data == null || data.isEmpty()) {
            return;
        }

        Map headMap = new LinkedHashMap();
        headMap.put("email", "邮箱账号");
        headMap.put("firstName", "用户名称");
        headMap.put("phone", "电话号码");
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(data));
        try {
            ExcelUtil.downloadExcelFile("用户列表", headMap, jsonArray, response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @PostMapping("/user/import")
    public Map importData(MultipartFile file, String roleId, String projectIdsString) {
        Map result = new HashMap();
        try {
            User user;
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum() + 1;
            // 从第二行开始
            List<User> userList = new ArrayList<>();
            for (int i = 2; i < lastRowNum; i++) {
                try {
                    user = new User();
                    Row row = sheet.getRow(i);
                    // 邮箱
                    user.setEmail(row.getCell(0) == null ? "" : String.valueOf(row.getCell(0)));
                    // 用户名
                    user.setFirstName(row.getCell(1) == null ? "" : String.valueOf(row.getCell(1)));
                    user.setLastName(row.getCell(1) == null ? "" : String.valueOf(row.getCell(1)));
                    // 电话
                    user.setPhone(row.getCell(2) == null ? "" : String.valueOf(row.getCell(2)));

                    user.setPassword("123456");
                    user.setCustomerId(new CustomerId(UUIDConverter.fromString(DataConstants.DEFAULT_CUSTOMER_USER_ID)));
                    user.setAuthority(Authority.CUSTOMER_USER);

                    User saveUser = this.saveUser(user);
                    userList.add(saveUser);
                } catch (ThingsboardException e) {
                    log.error("导入用户行失败!", e);
                }
            }

            // 添加角色和项目
            for (User newUser : userList) {
                try {
                    // 角色
                    roleService.assignRoleToUser(newUser.getId(), new RoleId(UUIDConverter.fromString(roleId)));

                    // 项目
                    List<String> projectIdList = Arrays.asList(projectIdsString.split(","));
                    this.assignUser(projectIdList, newUser.getUuidId().toString());
                } catch (ThingsboardException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();

            result.put("code", "500");
            result.put("msg", e.getMessage());
            return result;
        }
        result.put("code", "0");
        result.put("msg", "导入成功");
        return result;

    }

    @GetMapping("userinfo")
    public User userInfo() throws ThingsboardException {
        return userService.findUserById(getCurrentUser().getId());
    }

    @PostMapping("user/saveUserCid")
    public IstarResponse saveUserCid(@RequestBody JSONObject param) {
        String userId = param.getString("userId");
        String cid = param.getString("cid");
        appMsgPushService.saveUserCid(userId, cid);

        return IstarResponse.ok();
    }

    private void invalidateUserCache(User user) {
        UserId id = user.getId();
        if (id == null)
            return;

        invalidateUserCache(UUIDConverter.fromTimeUUID(id.getId()));
    }

    private void invalidateUserCache(String uuid) {
        Environment.getEnvironment().invalidateUsernameCache(uuid);
    }

}
