<!-- 用户管理 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    />
    <CardTable
      class="card-table"
      :config="TableConfig"
    />
    <DialogForm
      ref="refForm_Profile"
      :config="FormConfig_Profile"
    ></DialogForm>
    <DialogForm
      ref="refForm_Aou"
      :config="FormConfig_Aou"
    ></DialogForm>
    <DialogForm
      ref="refForm_Character"
      :config="FormConfig_Character"
    ></DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef } from 'vue'
import { ElMessage } from 'element-plus'
import {
  assignProjectsToUsers,
  assignRoleToUser,
  getRoleIdByUserId,
  getRolesByPage
} from '@/api/menu'
import {
  getUserList,
  deleteUsers,
  disableUser,
  enableUser,
  resetPWD,
  importUserList,
  exportUserList,
  saveUser
} from '@/api/user' // saveUser,
// import { parse } from 'semver'

import { addSlash, removeSlash } from '@/utils/removeIdSlash'
import useGlobal from '@/hooks/global/useGlobal'
import ImportButtonVue from '@/components/Form/ImportButton.vue'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { useBusinessStore, useUserStore } from '@/store'
import { checkEmail, checkPhone } from '@/utils/formValidate'
import { downloadUrl, ExportReport } from '../yinshou/baobiao'
import { getProjectRelationByEntityTypeAndEntityId } from '@/api/project'
import { getWaterSupplyTree } from '@/api/company_org'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refSearch = ref<ISearchIns>()
const refForm_Aou = ref<IDialogFormIns>()
const refForm_Character = ref<IDialogFormIns>()
const refForm_Profile = ref<IDialogFormIns>()
const tenantListDialog = reactive<{
  visible: boolean
  userId: string
  close:() => any
    }>({
      visible: false,
      userId: '',
      close: () => {
        tenantListDialog.visible = false
      }
    })
const SearchConfig = reactive<ISearch>({
  defaultParams: { status: '1' },
  // size: 'small',
  labelWidth: 40,
  filters: [
    { label: '搜索', field: 'name', type: 'input' },
    {
      label: '状态',
      field: 'status',
      type: 'select',
      options: [
        { label: '启用', value: '1' },
        { label: '停用', value: '0' }
      ]
    },
    { label: '角色', field: 'roleId', type: 'select', options: [] }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true,
          text: '查询',
          click: () => {
            TableConfig.selectList = []
            refreshData()
          } },
        {
          text: '添加用户',
          disabled: () => useUserStore().roles[0] === 'SYS_ADMIN',
          click: () => handleAou(),
          perm:
            $btnPerms('UserManageAdd') || useUserStore().roles[0] === 'SYS_ADMIN'
        },
        {
          text: '权限赋予',
          click: () => projectDispatch(),
          disabled: () => !TableConfig.selectList?.length,
          perm: $btnPerms('UserManageAuth')
        },
        {
          text: '批量删除',
          type: 'danger',
          disabled: () => !TableConfig.selectList?.length,
          click: () => haneleDelete(),
          perm: $btnPerms('UserManageMultiDelete')
        },
        {
          text: '下载模板',
          perm: $btnPerms('UserManageDownloadTemp'),
          click: () => downloadTemplate()
        },
        {
          text: '导出',
          type: 'warning',
          perm: $btnPerms('UserManageExport'),
          click: () => exportUser()
        },
        {
          text: '导入',
          perm: $btnPerms('UserManageImport'),
          click: (file, roleId, projectIdsString) => importUser(file, roleId, projectIdsString),
          component: shallowRef(ImportButtonVue)
        }
      ]
    }
  ]
})

const TreeData = reactive<SLTreeConfig>({
  title: '组织架构',
  data: [],
  currentProject: {},
  isFilterTree: true,
  treeNodeHandleClick: data => {
    // 设置当前选中项目信息
    TreeData.currentProject = data
    useBusinessStore().SET_selectedProject(data)
    refreshData()
  }
})

const organize = reactive<any>({
  organizeData: [],
  getorganize: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      const data = res.data?.data || []
      organize.organizeData = data || []
      TreeData.data = data || []
      TreeData.currentProject = data[0]
      refreshData()
    })
  },
  isorganize: val => {
    if (val === undefined) return false
    const item = deepQuery(organize.organizeData, val)
    if (item.layer && item.layer !== 2) {
      SLMessage.warning('请选择部门')
      return false
    }
    if (item.data && item.data.layer && item.data.layer !== 2) {
      SLMessage.warning('请选择部门')
      return false
    }
    return true
  }
})
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  handleSelectChange: rows => {
    TableConfig.selectList = rows || []
  },
  columns: [
    //   { prop: 'loginName', label: '工号' },
    { prop: 'firstName', label: '姓名' },
    { prop: 'departmentName', label: '部门' },
    { prop: 'roleName', label: '角色' },
    { prop: 'phone', label: '手机号' }
    // { prop: 'roleName', label: '角色' }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      text: '企业赋予',
      isTextBtn: true,
      icon: 'iconfont icon-guanli',
      disabled: (row: any): boolean => tenantSetDisable(row),
      perm: false,
      click: row => clickSetTenant(row)
    },
    {
      text: '角色赋予',
      isTextBtn: true,
      perm: $btnPerms('UserManageAuthRole'),
      icon: 'iconfont icon-jiaose',
      click: row => roleDispatch(row)
    },
    {
      text: '权限范围',
      isTextBtn: true,
      perm: $btnPerms('UserManagePermArea'),
      icon: 'iconfont icon-quanxian',
      click: row => projectDispatch(row)
    },
    {
      text: row => (row.status ? '停用' : '启用'),
      isTextBtn: true,
      icon: row => (row.status
        ? 'iconfont icon-qitingcaozuo-tingzhi'
        : 'iconfont icon-jiechuhezuoguanxi'),
      // icon: 'iconfont icon-qitingcaozuo-tingzhi',
      perm: $btnPerms('UserManageStop'),
      click: row => {
        if (row.status) {
          clickDisableUser(row)
          return
        }
        clickEnableUser(row)
      }
    },
    {
      text: '重置密码',
      isTextBtn: true,
      icon: 'iconfont icon-jiechuhezuoguanxi',
      perm: $btnPerms('UserManageRestPass'),
      click: row => clickResetPWD(row)
    },
    {
      text: '编辑',
      isTextBtn: true,
      icon: 'iconfont icon-bianji',
      perm: $btnPerms('UserManageEdit'),
      click: row => handleAou(row)
    },
    {
      text: '删除',
      isTextBtn: true,
      type: 'danger',
      perm: $btnPerms('UserManageDelete'),
      icon: 'iconfont icon-shanchu1',
      click: row => haneleDelete(row)
    }
  ],
  operationWidth: '540px'
})
const FormConfig_Aou = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  labelWidth: 120,
  title: '添加用户',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '姓名',
          field: 'firstName',
          rules: [{ required: true, message: '请输入用户姓名' }]
        }, {
          type: 'password',
          label: '账户密码',
          field: 'password',
          hidden: computed(() => FormConfig_Aou.title === '编辑用户'), // 编辑时隐藏
          rules: [{ required: true, message: '请输入密码' }]
        },
        {
          type: 'password',
          label: '确认密码',
          field: 'password1',
          hidden: computed(() => FormConfig_Aou.title === '编辑用户'), // 编辑时隐藏
          rules: [{ required: true, message: '请确认密码' }]
        },
        {
          type: 'input',
          label: '电话号码',
          field: 'phone',
          rules: [{ required: true, validator: checkPhone, trigger: 'blur' }]
        },
        {
          type: 'input',
          label: '邮箱',
          field: 'email',
          rules: [{ required: true, validator: checkEmail, trigger: 'blur' }]
        },
        {
          type: 'select-tree',
          label: '所属部门',
          field: 'departmentId',
          defaultExpandAll: true,
          checkStrictly: true,
          options: computed(() => traverse(organize.organizeData)) as any,
          onChange: val => {
            if (!organize.isorganize(val)) {
              refForm_Aou.value?.refForm
                && refForm_Aou.value?.refForm?.dataForm
                && (refForm_Aou.value.refForm.dataForm.departmentId = '')
            }
            return val
          },
          rules: [{ required: true, message: '请选择所属部门' }]
        },
        {
          hidden: useUserStore().roles[0] !== 'SYS_ADMIN',
          type: 'select',
          label: '角色配置',
          field: 'authority',
          onChange: val => {
            const extraParams = val === 'TENANT_SUPPORT' ? { jszc: false } : { sctg: false }
            const formData = refForm_Aou.value?.refForm?.dataForm || {}
            delete formData.jszc
            delete formData.sctg
            FormConfig_Aou.defaultValue = {
              ...formData,
              ...extraParams
            }
            refForm_Aou.value?.resetForm()
          },
          options: [
            { label: '技术支持', value: 'TENANT_SUPPORT' },
            { label: '市场推广', value: 'TENANT_PROMOTE' }
          ],
          rules: [{ required: true, message: '请选择用户角色配置' }]
        },
        {
          hidden: true,
          handleHidden: (params: any, query: any, config) => {
            config.hidden = params.authority !== 'TENANT_SUPPORT'
              || useUserStore().roles[0] !== 'SYS_ADMIN'
          },
          type: 'radio',
          label: '固件升级权限',
          field: 'jszc',
          options: [
            { label: '开启', value: true },
            { label: '关闭', value: false }
          ],
          rules: [{ required: true, message: '请选择' }]
        },
        {
          hidden: true,
          handleHidden: (params: any, query: any, config) => {
            config.hidden = params.authority !== 'TENANT_PROMOTE'
              || useUserStore().roles[0] !== 'SYS_ADMIN'
          },
          type: 'radio',
          label: '是否代理商',
          field: 'sctg',
          options: [
            { label: '是', value: true },
            { label: '否', value: false }
          ],
          rules: [{ required: true, message: '请选择' }]
        }
      ]
    }
  ],
  submit: params => {
    // 如果是编辑模式，且密码字段被隐藏，则不进行密码验证
    if (FormConfig_Aou.title === '编辑用户') {
      SLConfirm('确定提交？', '提示信息')
        .then(async () => {
          FormConfig_Aou.submitting = true
          try {
            const submitParams = {
              ...params,
              customerId: {
                entityType: 'CUSTOMER',
                id: 'b49ef6e0-8100-11e8-9cbe-77a34f885729'
              },
              authority: 'CUSTOMER_USER'
            }
            // 在编辑模式下，删除密码相关字段，确保不发送密码到后端
            delete submitParams.password
            delete submitParams.password1

            await saveUser(submitParams)
            SLMessage.success('操作成功')
            refForm_Aou.value?.closeDialog()
            refreshData()
          } catch (error: any) {
            SLMessage.error(error)
          }
          FormConfig_Aou.submitting = false
        })
        .catch(() => {
          //
        })
      return
    }

    // 添加用户时，进行密码一致性检查
    if (params.password !== params.password1) {
      ElMessage.warning('密码不一致，请确认密码')
      return
    }
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig_Aou.submitting = true
        try {
          const submitParams = {
            ...params,
            customerId: {
              entityType: 'CUSTOMER',
              id: 'b49ef6e0-8100-11e8-9cbe-77a34f885729'
            },
            authority: 'CUSTOMER_USER'
          }
          await saveUser(submitParams)
          SLMessage.success('操作成功')
          refForm_Aou.value?.closeDialog()
          refreshData()
        } catch (error: any) {
          SLMessage.error(error)
        }
        FormConfig_Aou.submitting = false
      })
      .catch(() => {
        //
      })
  }
})
const FormConfig_Character = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  labelWidth: 60,
  title: '角色赋予',
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          label: '角色',
          placeholder: '请选择角色',
          field: 'roleId',
          multiple: true,
          options: []
        }
      ]
    }
  ],
  submit: params => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig_Character.submitting = true
        try {
          const roleIds = params.roleId.map(data => {
            return addSlash(data)
          })
          // params.roleId = addSlash(params.roleId)
          params.roleId = roleIds.join(',')
          await assignRoleToUser(params)
          SLMessage.success('提交成功')
          refreshData()
          refForm_Character.value?.closeDialog()
        } catch (error) {
          SLMessage.error('操作失败')
        }
        FormConfig_Character.submitting = false
      })
      .catch(() => {
        //
      })
  }
})
const roleDispatch = async row => {
  const res = await getRoleIdByUserId(row.id.id)
  FormConfig_Character.defaultValue = {
    userId: row.id.id,
    roleId: res.data && removeSlash(res.data)
  }
  refForm_Character.value?.openDialog()
}
const FormConfig_Profile = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  labelWidth: 60,
  title: '权限范围',
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          label: '项目',
          field: 'projectIds',
          showCheckbox: true,
          options: useBusinessStore().projectList,
          checkStrictly: true,
          multiple: true
        }
      ]
    }
  ],
  submit: params => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig_Profile.submitting = true
        try {
          await assignProjectsToUsers(params)
          SLMessage.success('提交成功')
          refreshData()
          refForm_Profile.value?.closeDialog()
        } catch (error) {
          SLMessage.error('操作失败')
        }
        FormConfig_Profile.submitting = false
      })
      .catch(() => {
        //
      })
  }
})
const refreshData = async () => {
  // TableConfig.selectList = []
  const query = refSearch.value?.queryParams || {}
  const paramsObj = {
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    authority: 'CUSTOMER_USER',
    pid: TreeData.currentProject?.id || '',
    ...(query || {})
  }
  const res = await getUserList(paramsObj)
  const data = res.data?.data?.data?.map(element => {
    if (element.additionalInfo && typeof element.additionalInfo === 'string') {
      element.additionalInfo = JSON.parse(element.additionalInfo)
    }
    if (element.authority === 'TENANT_ADMIN') {
      element.authority = '企业管理人员'
    } else if (element.authority === 'TENANT_SYS') {
      element.authority = '企业配置人员'
    } else if (element.authority === 'CUSTOMER_USER') {
      element.authority = '企业用户'
    } else if (element.authority === 'SYS_ADMIN') {
      element.authority = '超级管理员'
    } else if (element.authority === 'TENANT_SUPPORT') {
      element.authority = '技术支持'
    } else if (element.authority === 'TENANT_PROMOTE') {
      element.authority = '市场推广'
    }
    return element
  })
  TableConfig.dataList = [...data]
  TableConfig.pagination.total = res.data?.data?.total || 0
}

// 角色查询列表
const initSearchRoleOption = async () => {
  const res = await getRolesByPage({
    size: 9999999,
    page: 1
  })
  const filter = SearchConfig.filters?.find(
    item => item.field === 'roleId'
  ) as IFormSelect
  filter
    && (filter.options = res.data?.data?.map(role => ({
      label: role.name,
      value: removeSlash(role.id.id)
    })))
  const formFilter = FormConfig_Character.group[0].fields[0] as IFormSelect
  formFilter && (formFilter.options = filter.options)
}
const haneleDelete = (row?: any) => {
  SLConfirm('确定删除?', '删除提示').then(async () => {
    // 删除角色方法
    let ids: any[] = []
    if (row) {
      ids = [row.id.id]
    } else {
      ids = TableConfig.selectList?.map(item => item.id.id) || []
    }
    if (!ids.length) {
      SLMessage.warning('请选择要删除的用户')
      return
    }
    try {
      await deleteUsers(ids)
      SLMessage.success('删除成功')
      refreshData()
    } catch (error) {
      SLMessage.error('删除失败')
    }
  })
}
// 点击 项目赋予
const projectDispatch = async (row?: any) => {
  const userIds: string[] = (row && [row.id.id]) || TableConfig.selectList?.map(item => item.id.id)
  if (!userIds.length) {
    SLMessage.warning('请先选择用户')
    return
  }
  const res = await getProjectRelationByEntityTypeAndEntityId(
    'USER',
    userIds[0]
  )
  // res.data.forEach(item => {
  //   this.$refs.treeSelect.addValue(item)
  // })
  console.log(res.data)

  FormConfig_Profile.defaultValue = {
    userIds,
    projectIds: res.data?.map(item => item.id) || []
  }
  refForm_Profile.value?.resetForm()
  refForm_Profile.value?.openDialog()
}
const handleAou = (row?: any) => {
  FormConfig_Aou.title = row ? '编辑用户' : '添加用户'
  if (!row && TreeData.currentProject.layer !== 2) {
    ElMessage.warning('请选中部门')
    return
  }
  FormConfig_Aou.defaultValue = {
    ...(row || {})
  }
  refForm_Aou.value?.resetForm()
  refForm_Aou.value?.openDialog()
}
const clickDisableUser = row => {
  SLConfirm('确定停用该用户?', '提示信息')
    .then(async () => {
      try {
        const id = removeSlash(row.id.id)
        await disableUser(id)
        SLMessage.success('已停用该用户')
        refreshData()
      } catch (error) {
        SLMessage.warning('停用失败')
      }
    })
    .catch(() => {
      //
    })
}
const clickEnableUser = row => {
  SLConfirm('确定启用?', '提示信息')
    .then(async () => {
      try {
        const id = removeSlash(row.id.id)
        await enableUser(id)
        SLMessage.success('已启用用户，当前用户可以正常登录')
        refreshData()
      } catch (error) {
        SLMessage.warning('启用失败')
      }
    })
    .catch(() => {
      //
    })
}
const clickResetPWD = async row => {
  SLConfirm('确定重置密码吗?', '删除提示').then(async () => {
    try {
      const res = await resetPWD(removeSlash(row.id.id))
      if (res.status === 200) {
        SLMessage.success('重置成功')
      }
    } catch (err: any) {
      SLMessage.error(err.data.message || '重置失败')
    }
  })
}
const tenantSetDisable = row => {
  return row.authority !== '市场推广'
}
const init = () => {
  const isSysAdmin = useUserStore().roles[0] === 'SYS_ADMIN'
  if (isSysAdmin) {
    // 开启按钮
    const sysAdminBtnPerms = [
      '企业赋予',
      '重置密码',
      '启用',
      '停用',
      '编辑',
      '删除'
    ]
    for (const item of TableConfig.operations || []) {
      item.perm = sysAdminBtnPerms.some(key => key === item.text)
    }
  }
}
const clickSetTenant = row => {
  tenantListDialog.userId = row.id.id
  tenantListDialog.visible = true
}

const importUser = async (formData, roleId, projectIdsString) => {
  formData.append('roleId', removeSlash(roleId))
  formData.append('projectIdsString', projectIdsString)
  const res = await importUserList(formData)
  if (~~res.data.code === 0 && ~~res.status === 200) {
    SLMessage.success('导入成功')
    refreshData()
  }
}
const exportUser = async () => {
  const res = await exportUserList()
  ExportReport(res.data, '用户列表.xlsx')
}

function deepQuery(tree, id) {
  tree = JSON.parse(JSON.stringify(tree))
  let isGet = false
  let retNode = null
  function deepSearch(tree, id) {
    for (let i = 0; i < tree.length; i++) {
      if (tree[i].children && tree[i].children.length > 0) {
        deepSearch(tree[i].children, id)
      }
      if (id === tree[i].id || isGet) {
        isGet || (retNode = tree[i])
        isGet = true
        break
      }
    }
  }
  deepSearch(tree, id)
  return retNode as any
}

const downloadTemplate = () => downloadUrl('http://*************:9950/user_template.xlsx', '设备模板.xlsx')
onMounted(() => {
  init()
  refreshData()
  initSearchRoleOption()
  organize.getorganize()
})
</script>

<style lang="scss" scoped>
.tree-right-detail-box {
  height: 100%;
  width: 100%;
  margin: 15px;
}

.cardSearch {
  margin: 0;
  margin-bottom: 15px;
}

.card-table {
  height: calc(100% - 75px);
}
</style>
